const dayjs = require('../boot/dayjs');
const BaseService = require('../base/serviceFn');
const {
  SHIPPING_TYPE_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  ALLOC_STATUS,
  INVENTORY_CONTROL_TYPE_ENUM,
  INVENTORY_TYPE_ENUM,
  VOLUME_TYPE_ENUM,
  SHIPPING_INVENTORY_TYPE_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
} = require('../helpers/enum');
const ShortUniqueId = require('short-unique-id');
const { MESSAGE } = require('../utils/message');
const {
  InvalidPermissionException,
  ControlledException,
  BadRequestException,
} = require('../base/errors');

class OutboundService extends BaseService {
  // ====== 1. Private method ==========
  /**
   * Calculate actual shipping date
   * @param {Date} shippingDate - shipping date
   * @returns {Object} - actual shipping date
   */
  #calcShippingDate(shippingDate) {
    const dateTimeNow = dayjs.getDate();
    const shippingDateTime = dayjs.getDateFromJST(`${shippingDate} 23:59:59`);

    return { dateTimeNow, shippingDateTime };
  }

  /**
   * Check code validation
   * @param {string} code - code from user
   * @param {string} userCode - user code
   * @returns {Promise<boolean>}
   */
  async #checkCodeValidation(code, userCode) {
    // check enterprise code/apply code validation
    if (code.slice(0, 7) !== userCode.slice(0, 7)) {
      return false;
    }

    // check if the next 6 characters are a date format
    if (!dayjs(code.slice(7, 13), 'YYMMDD', true).isValid()) {
      return false;
    }

    return true;
  }

  /**
   * Check permission for proxy outbound shipment
   * @param {object} user - user information
   * @param {object} proxyUser - proxy user information
   * @returns {Promise<boolean>}
   */
  async #checkPermissionForProxyOutboundShipment(proxyUser) {
    // check if proxy user exists and has the same destination as the user
    if (!proxyUser) {
      return false;
    }

    // all checks passed
    return true;
  }

  /**
   * Check if ingredient is valid
   * @param {number} userId - user id
   * @param {number} weightAlertThreshold - weight alert threshold
   * @param {number} inventoryId - inventory id
   * @param {object} shipmentInfo - information of shipment to be taken from inventory
   * @returns {Promise<object[]>} { flag: number, selectedInfo: object }
   * flag:-1 - invalid, 0 - valid, 1 - alert, 2 - need to give reason
   * selectedInfo: { shipping_inventory_id, shipping_net_weight, taken_inventory_weight } | undefined - information of selected ingredient
   * inventoryInfo: {} | undefined - information of inventory
   */
  async #checkValidationIngredient(userId, weightAlertThreshold, inventoryId, shipmentInfo) {
    const connect = this.DB.READ;
    const inventory = await connect.inventories.findFirst({
      where: {
        id: Number(inventoryId),
        user_id: userId,
        delete_flag: false,
      },
      select: {
        id: true,
        group_name: true,
        gross_weight_inventory: true,
        net_weight_inventory: true,
        tare_weight_inventory: true,
        net_weight_total: true,
        current_arrival_id_list: true,
        user_id: true,
        the_origins: {
          select: {
            id: true,
            code: true,
            starting_user_name: true,
            starting_enterprise_name: true,
            shipping_date: true,
            arrival_net_weight: true,
          },
        },
      },
    });

    if (!inventory) {
      return false;
    }

    const { gross_weight, tare_weight } = shipmentInfo;
    const netWeight = gross_weight - tare_weight;

    const selectedInfo = {
      shipping_inventory_id: inventoryId,
      shipping_weight: netWeight,
      taken_inventory_weight: Math.min(inventory.net_weight_inventory, netWeight),
      gross_weight,
      tare_weight,
    };
    inventory.taken_inventory_weight = selectedInfo.taken_inventory_weight;
    // case invalid
    if (netWeight < 0) {
      return {
        flag: -1,
        selectedInfo: undefined,
        inventoryInfo: undefined,
      };
    }

    // case alert
    // it means the net weight is greater than the inventory weight but doesn't need provide reason
    if (
      inventory.net_weight_inventory <= netWeight &&
      (netWeight / inventory.net_weight_inventory) * 100 <= weightAlertThreshold + 100
    ) {
      return {
        flag: 1,
        selectedInfo,
        inventoryInfo: inventory,
      };
    }

    // case need to give reason
    if ((netWeight / inventory.net_weight_inventory) * 100 > weightAlertThreshold + 100) {
      return {
        flag: 2,
        selectedInfo,
        inventoryInfo: inventory,
      };
    }

    // case valid - normal
    return {
      flag: 0,
      selectedInfo,
      inventoryInfo: inventory,
    };
  }

  // ====== 2. Public method ==========
  /**
   * Register outbound shipment
   * @param {object} user - user information
   * @param {object} body - information of outbound shipment
   * @returns {Promise<object>} - response object { id, code, shipping_date, shipping_gross_weight, shipping_tare_weight, shipping_quantity, ingredient, setting }
   */
  async registerOutboundShipment(user, body) {
    // get connection
    const connect = this.DB.WRITE;

    // get information from body
    const {
      code,
      shipper,
      date,
      gross_weight,
      tare_weight,
      // ingredient interface
      // {
      //   shipping_inventory_id,
      //   gross_weight,
      //   tare_weight,
      // }[]
      ingredient,
      type_diff,
      reason_diff,
      setting,
      inventory_type,
      code_suffix_id,
    } = body;

    // generate qr code for outbound shipment (16 characters) using alphanum dictionary
    // Example: 1a2b3c4d5e6f7g8h
    const qrCode = new ShortUniqueId({
      length: 16,
      dictionary: 'alphanum',
    }).randomUUID();

    // calculate actual shipping date
    const { dateTimeNow, shippingDateTime } = this.#calcShippingDate(date);

    // get starting user information
    const startingUser = await connect.users.findFirst({
      where: {
        id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        role: true,
        name: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get destination user information
    const destinationUser = await connect.users.findFirst({
      where: {
        id: shipper,
        delete_flag: false,
      },
      select: {
        id: true,
        name: true,
        role: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
            type: true,
          },
        },
      },
    });
    // get user apply information
    const applyUser = await connect.users.findFirst({
      where: {
        enterprise_id: user.enterprise_id,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
        delete_flag: false,
      },
      select: {
        id: true,
        setting: {
          select: {
            inventory_control_type: true,
          },
        },
      },
    });
    if (!startingUser || !destinationUser || !applyUser) {
      throw new BadRequestException();
    }

    // validation ingredient
    const systemSetting = await connect.sys_settings.findFirst({
      where: {
        setting_name: ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD,
        delete_flag: false,
      },
      select: {
        setting_value: true,
      },
    });
    // check ingredient validation
    // reassigned ingredientValidates to get the flag and selectedInfo
    const ingredientValidates = await Promise.all(
      ingredient.map((item) => {
        return this.#checkValidationIngredient(
          applyUser.setting?.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
            ? user.id
            : applyUser.id,
          systemSetting ? Number(systemSetting.setting_value) : 0,
          Number(item.shipping_inventory_id),
          item
        );
      })
    );
    const ingredientValidatesFlag = ingredientValidates.map((item) => item.flag);
    // if flag is -1, then return invalid ingredient error
    if (ingredientValidatesFlag.includes(-1)) {
      return this.ERROR(MESSAGE.MSG_INVALID_INGREDIENT_ERROR);
    }
    // if flag is 2, then need to give reason
    if (ingredientValidatesFlag.includes(2)) {
      if (!type_diff) {
        return this.ERROR(MESSAGE.TYPE_DIFFERENCE_REQUIRED);
      }
    }

    // create outbound shipment
    const outboundShipment = await connect.$transaction(async (tx) => {
      // handle for code suffix
      const codeSuffixBody = code.slice(-3);
      const codeSuffix = await tx.code_suffixes_alloc.findFirst({
        where: {
          id: code_suffix_id,
        },
        select: {
          code_suffix: true,
        },
      });
      // if code suffix is not exist or not match with code suffix body
      // then create a new code suffix
      if (codeSuffix && codeSuffix.code_suffix === codeSuffixBody) {
        await tx.code_suffixes_alloc.update({
          where: {
            id: code_suffix_id,
          },
          data: {
            alloc_status: ALLOC_STATUS.COMPLETED,
            latest_updated_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
          },
        });
      } else {
        await tx.code_suffixes_alloc.create({
          data: {
            code_group_key: code.slice(0, 13),
            code_suffix: codeSuffixBody,
            alloc_status: ALLOC_STATUS.COMPLETED,
            created_by_id: user.id,
            created_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dayjs().toDate(),
          },
        });
        await tx.code_suffixes_alloc.delete({
          where: {
            id: code_suffix_id,
          },
        });
      }
      // calc net weight
      const netWeight = gross_weight - tare_weight;
      // calculate ingredient after validation
      const calcIngredient = ingredientValidates.map((item) => ({
        shipping_inventory_id: item.selectedInfo.shipping_inventory_id,
        taken_inventory_weight: item.selectedInfo.taken_inventory_weight,
        shipping_weight: item.selectedInfo.shipping_weight,
        gross_weight: item.selectedInfo.gross_weight,
        tare_weight: item.selectedInfo.tare_weight,
      }));
      // calc shipping id list
      const shippingIdList = ingredientValidates
        .map((item) => item.inventoryInfo)
        .flat();
      // remove duplicates
      const calcShippingIdList = Array.from(
        new Map(shippingIdList.map((item) => [item.id, item])).values()
      );

      // create new shipment
      const newOutboundShipment = await tx.the_origins.create({
        data: {
          code,
          shipping_date: shippingDateTime.toDate(),
          shipping_gross_weight: gross_weight,
          shipping_tare_weight: tare_weight,
          shipping_net_weight: netWeight,
          shipping_quantity: 0,
          shipping_type: SHIPPING_TYPE_ENUM.NORMAL,
          created_by_id: user.id,
          latest_updated_by_id: user.id,
          created_on: dateTimeNow.toDate(),
          latest_updated_on: dateTimeNow.toDate(),
          qr_code: qrCode,
          destination_enterprise_id: destinationUser.enterprise_id,
          starting_enterprise_id: startingUser.enterprise_id,
          ingredient: calcIngredient,
          setting,
          destination_user_id: destinationUser.id,
          starting_user_id: startingUser.id,
          shipping_type_diff: type_diff,
          shipping_reason_diff: reason_diff,
          destination_enterprise_name: destinationUser.enterprise?.enterprise_name,
          destination_user_name: destinationUser.name,
          destination_license_number: destinationUser.license_number,
          destination_user_note_1: destinationUser.note_1,
          destination_user_note_2: destinationUser.note_2,
          starting_enterprise_name: startingUser.enterprise?.enterprise_name,
          starting_user_name: startingUser.name,
          starting_license_number: startingUser.license_number,
          starting_user_note_1: startingUser.note_1,
          starting_user_note_2: startingUser.note_2,
          shipping_inventory_type: inventory_type,
          shipping_id_list: calcShippingIdList,
        },
      });

      // create inventories_history
      const inventoriesHistoryData = ingredientValidates.map((item) => ({
        inventory_id: item.inventoryInfo.id,
        group_name: item.inventoryInfo.group_name,
        gross_weight_inventory: item.inventoryInfo.gross_weight_inventory,
        new_tare_weight_inventory:
          item.inventoryInfo.tare_weight_inventory - item.selectedInfo.tare_weight,
        net_weight_inventory: item.inventoryInfo.net_weight_inventory,
        new_gross_weight_inventory:
          item.inventoryInfo.gross_weight_inventory - item.selectedInfo.gross_weight,
        tare_weight_inventory: item.inventoryInfo.tare_weight_inventory,
        new_net_weight_inventory:
          item.inventoryInfo.net_weight_inventory - item.selectedInfo.shipping_weight,
        net_weight_total: item.inventoryInfo.net_weight_total,
        latest_arrival_date: dateTimeNow.toDate(),
        created_by_id: user.id,
        latest_updated_by_id: user.id,
        created_on: dateTimeNow.toDate(),
        latest_updated_on: dateTimeNow.toDate(),
        user_id: item.inventoryInfo.user_id,
        type_diff: TYPE_DIFFERENCE_WEIGHT_ENUM.BY_SYSTEM,
        current_arrival_id_list: item.inventoryInfo.current_arrival_id_list,
        new_current_arrival_id_list: [1, 2].includes(item.flag)
          ? []
          : item.inventoryInfo.current_arrival_id_list,
        is_display_inventories_history: false,
      }));

      await tx.inventories_history.createMany({
        data: inventoriesHistoryData,
      });
      if (destinationUser.enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN) {
        await tx.users.update({
          where: {
            id: user.id,
          },
          data: {
            enable_export_function: false,
          },
        })
      }
      // update inventory
      await Promise.all(
        ingredientValidates.map(async (item) => {
          await tx.inventories.update({
            where: {
              id: item.inventoryInfo.id,
            },
            data: {
              net_weight_inventory: {
                decrement: item.selectedInfo.taken_inventory_weight,
              },
              gross_weight_inventory: {
                decrement: item.selectedInfo.gross_weight,
              },
              tare_weight_inventory: {
                decrement: item.selectedInfo.tare_weight,
              },
              // reset current_arrival_id_list if flag is 1 or 2
              // It means the inventory is being empty
              current_arrival_id_list: [1, 2].includes(item.flag) ? [] : undefined,
              is_history_cancel_locked: true,
              latest_updated_on: dateTimeNow.toDate(),
              latest_updated_by_id: user.id,
            },
          });
        })
      );

      // TODO: query data to return for print PDF
      return newOutboundShipment;
    });

    return this.SUCCESS(outboundShipment);
  }

  /**
   * Register proxy outbound shipment
   * @param {object} user - user information
   * @param {object} body - information of outbound shipment
   * @returns {Promise<object>} - response object
   */
  async registerProxyOutboundShipment(user, body) {
    // get connection
    const connect = this.DB.WRITE;

    // get information from body
    const {
      code,
      date,
      volume_type,
      gross_weight,
      tare_weight,
      quantity,
      setting,
      session_token,
      code_suffix_id,
    } = body;

    // calculate actual shipping date
    const { dateTimeNow, shippingDateTime } = this.#calcShippingDate(date);

    // verify session token here to get starting user information
    const payload = this.FASTIFY.jwt.verify(session_token);
    if (!payload) {
      return this.ERROR(MESSAGE.MSG_INVALID_SESSION_TOKEN_ERROR);
    }

    // get starting user information
    const startingUser = await connect.users.findFirst({
      where: {
        id: Number(payload.id),
        delete_flag: false,
      },
      select: {
        id: true,
        role: true,
        name: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get destination user information
    const destinationUser = await connect.users.findFirst({
      where: {
        id: user.id,
        delete_flag: false,
      },
      select: {
        id: true,
        name: true,
        role: true,
        user_code: true,
        enterprise_id: true,
        enterprise_type: true,
        staff_type: true,
        license_number: true,
        note_1: true,
        note_2: true,
        enterprise: {
          select: {
            enterprise_name: true,
          },
        },
      },
    });
    // get user apply information
    const applyUser = await connect.users.findFirst({
      where: {
        enterprise_id: user.enterprise_id,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
        delete_flag: false,
      },
      select: {
        id: true,
        setting: {
          select: {
            inventory_control_type: true,
          },
        },
      },
    });
    if (!startingUser || !destinationUser || !applyUser) {
      throw new BadRequestException();
    }

    // check permission for proxy outbound shipment
    const hasPermission = await this.#checkPermissionForProxyOutboundShipment(startingUser);
    if (!hasPermission) {
      throw new InvalidPermissionException();
    }

    // check code validation here
    const codeValidation = await this.#checkCodeValidation(code, startingUser.user_code);
    if (!codeValidation) {
      throw new ControlledException();
    }

    // create outbound shipment
    const outboundShipment = await connect.$transaction(async (tx) => {
      // handle for code suffix
      const codeSuffixBody = code.slice(-3);
      const codeSuffix = await tx.code_suffixes_alloc.findFirst({
        where: {
          id: code_suffix_id,
        },
        select: {
          code_suffix: true,
        },
      });
      // if code suffix is not exist or not match with code suffix body
      // then create a new code suffix
      if (codeSuffix && codeSuffix.code_suffix === codeSuffixBody) {
        await tx.code_suffixes_alloc.update({
          where: {
            id: code_suffix_id,
          },
          data: {
            alloc_status: ALLOC_STATUS.COMPLETED,
            latest_updated_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
          },
        });
      } else {
        await tx.code_suffixes_alloc.create({
          data: {
            code_group_key: code.slice(0, 13),
            code_suffix: codeSuffixBody,
            alloc_status: ALLOC_STATUS.COMPLETED,
            created_by_id: user.id,
            created_on: dayjs().toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dayjs().toDate(),
          },
        });
        await tx.code_suffixes_alloc.delete({
          where: {
            id: code_suffix_id,
          },
        });
      }

      // calculate new group name
      const groupName =
        applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
          ? `${shippingDateTime.format('MM/DD')}集荷ロット`
          : `${shippingDateTime.format('MM/DD')}集荷ロット（${destinationUser.name}）`;
      // check group name of inventory is exist or not
      const checkInventoryIsExist = await tx.inventories.findFirst({
        where: {
          user_id:
            applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
              ? destinationUser.id
              : applyUser.id,
          delete_flag: false,
          group_name: groupName,
          // only check the inventory has same year as shipping date
          inventory_start_date: {
            gte: new Date(`${shippingDateTime.year()}-01-01T00:00:00.000Z`),
            lt: new Date(`${shippingDateTime.year() + 1}-01-01T00:00:00.000Z`),
          },
        },
      });

      // handle logic with the_origins
      const netWeight = gross_weight - tare_weight;
      const newOutboundShipment = await tx.the_origins.create({
        data: {
          code: code,
          shipping_date: dateTimeNow.toDate(),
          shipping_gross_weight: gross_weight,
          shipping_tare_weight: tare_weight,
          shipping_net_weight: netWeight,
          shipping_quantity: volume_type === VOLUME_TYPE_ENUM.QUANTITY ? quantity : 0,
          arrival_gross_weight: gross_weight,
          arrival_tare_weight: tare_weight,
          arrival_net_weight: netWeight,
          arrival_quantity: volume_type === VOLUME_TYPE_ENUM.QUANTITY ? quantity : 0,
          arrival_date: shippingDateTime.toDate(),
          shipping_type: SHIPPING_TYPE_ENUM.PROXY,
          created_by_id: startingUser.id,
          latest_updated_by_id: user.id,
          created_on: dateTimeNow.toDate(),
          latest_updated_on: dateTimeNow.toDate(),
          destination_enterprise_id: destinationUser.enterprise_id,
          starting_enterprise_id: startingUser.enterprise_id,
          setting: {
            [volume_type === VOLUME_TYPE_ENUM.QUANTITY
              ? 'price_per_quantity'
              : 'price_per_kilogram']: setting.price || undefined,
            display_shipment_weight: setting.display_shipment_weight,
          },
          destination_user_id: destinationUser.id,
          starting_user_id: startingUser.id,
          destination_enterprise_name: destinationUser.enterprise.enterprise_name,
          destination_user_name: destinationUser.name,
          destination_license_number: destinationUser.license_number,
          destination_user_note_1: destinationUser.note_1,
          destination_user_note_2: destinationUser.note_2,
          starting_enterprise_name: startingUser.enterprise.enterprise_name,
          starting_user_name: startingUser.name,
          starting_license_number: startingUser.license_number,
          starting_user_note_1: startingUser.note_1,
          starting_user_note_2: startingUser.note_2,
          shipping_inventory_type: SHIPPING_INVENTORY_TYPE_ENUM.DOMESTIC,
          ingredient: [],
          shipping_id_list: [],
        },
      });

      // handle for inventory
      let inventoryId = null;
      // if inventory is not exist
      if (!checkInventoryIsExist) {
        // create inventory
        const newInventory = await tx.inventories.create({
          data: {
            group_name: groupName,
            gross_weight_inventory: gross_weight,
            tare_weight_inventory: tare_weight,
            net_weight_inventory: netWeight,
            net_weight_total: netWeight,
            latest_arrival_date: dateTimeNow.toDate(),
            is_history_cancel_locked: true,
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            created_on: dateTimeNow.toDate(),
            latest_updated_on: dateTimeNow.toDate(),
            user_id:
              applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
                ? user.id
                : applyUser.id,
            current_arrival_id_list: [
              {
                id: newOutboundShipment.id,
                code: newOutboundShipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            inventory_type: INVENTORY_TYPE_ENUM.DOMESTIC,
            inventory_start_date: shippingDateTime.toDate(),
          },
        });
        inventoryId = newInventory.id;
      }
      // if inventory is exist
      else {
        inventoryId = checkInventoryIsExist.id;
        // create inventory history
        await tx.inventories_history.create({
          data: {
            inventory_id: inventoryId,
            group_name: groupName,
            gross_weight_inventory: checkInventoryIsExist.gross_weight_inventory,
            tare_weight_inventory: checkInventoryIsExist.tare_weight_inventory,
            net_weight_inventory: checkInventoryIsExist.net_weight_total,
            new_gross_weight_inventory: checkInventoryIsExist.gross_weight_inventory + gross_weight,
            new_tare_weight_inventory: checkInventoryIsExist.tare_weight_inventory + tare_weight,
            new_net_weight_inventory: checkInventoryIsExist.net_weight_total + netWeight,
            net_weight_total: checkInventoryIsExist.net_weight_total,
            latest_arrival_date: dateTimeNow.toDate(),
            created_by_id: user.id,
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            created_on: dateTimeNow.toDate(),
            user_id: destinationUser.id,
            type_diff: TYPE_DIFFERENCE_WEIGHT_ENUM.BY_SYSTEM,
            reason_diff: '',
            current_arrival_id_list: checkInventoryIsExist.current_arrival_id_list,
            new_current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              {
                id: newOutboundShipment.id,
                code: newOutboundShipment.code,
                starting_enterprise_name: startingUser.enterprise.enterprise_name,
                starting_user_name: startingUser.name,
              },
            ],
            is_display_inventories_history: true,
          },
        });

        // update inventory
        await tx.inventories.update({
          where: {
            id: inventoryId,
          },
          data: {
            gross_weight_inventory: {
              increment: gross_weight,
            },
            tare_weight_inventory: {
              increment: tare_weight,
            },
            net_weight_inventory: {
              increment: netWeight,
            },
            net_weight_total: {
              increment: netWeight,
            },
            latest_arrival_date: dateTimeNow.toDate(),
            latest_updated_by_id: user.id,
            latest_updated_on: dateTimeNow.toDate(),
            current_arrival_id_list: [
              ...checkInventoryIsExist.current_arrival_id_list,
              newOutboundShipment.id,
            ],
            is_history_cancel_locked: true,
          },
        });
      }

      // update outbound shipment with inventory id
      await tx.the_origins.update({
        where: {
          id: newOutboundShipment.id,
        },
        data: {
          inventory_id: inventoryId,
        },
      });

      return newOutboundShipment;
    });

    // get the outbound shipment result
    const outboundShipmentResult = await connect.the_origins.findFirst({
      where: {
        id: outboundShipment.id,
      },
      select: {
        id: true,
        code: true,
        shipping_date: true,
        shipping_gross_weight: true,
        shipping_tare_weight: true,
        shipping_net_weight: true,
        shipping_quantity: true,
        arrival_date: true,
        arrival_gross_weight: true,
        arrival_tare_weight: true,
        arrival_net_weight: true,
        qr_code: true,
        setting: true,
        arrival_quantity: true,
        destination_enterprise_name: true,
        destination_user_name: true,
        starting_enterprise_name: true,
        starting_user_name: true,
      },
    });

    return this.SUCCESS(outboundShipmentResult);
  }

  /**
   * Check permission for proxy outbound shipment
   * @param {object} user - user information
   * @param {number} proxyUserId - proxy user id
   * @returns {Promise<object>} - response object { hasPermission, shipper, supplier }
   */
  async checkPermissionForProxyOutboundShipment(user, proxyUserId) {
    // get connection
    const connect = this.DB.READ;

    // find proxy user
    const proxyUser = await connect.users.findFirst({
      where: {
        id: Number(proxyUserId),
        delete_flag: false,
      },
      select: {
        role: true,
        enterprise_id: true,
        setting: {
          select: {
            destination_id: true,
          },
        },
        enterprise_type: true,
        staff_type: true,
      },
    });

    // check permission for proxy outbound shipment
    const hasPermission = await this.#checkPermissionForProxyOutboundShipment(proxyUser);

    if (!hasPermission) {
      return this.SUCCESS({ hasPermission: false });
    }

    // all checks passed
    // calc shipper and supplier
    const shipper = await connect.enterprises.findFirst({
      where: {
        id: user.enterprise_id,
        delete_flag: false,
      },
      select: {
        id: true,
        enterprise_name: true,
        enterprise_name_kana: true,
        enterprise_code: true,
      },
    });

    const supplier = await connect.enterprises.findFirst({
      where: {
        id: proxyUser.enterprise_id,
        delete_flag: false,
      },
      select: {
        id: true,
        enterprise_name: true,
        enterprise_name_kana: true,
        enterprise_code: true,
      },
    });

    // calc pre price per gram
    const latestProxyOutboundShipment = await connect.the_origins.findFirst({
      where: {
        destination_user_id: user.id,
        delete_flag: false,
        // condition to get the proxy outbound shipment
        qr_code: null,
        shipping_type: SHIPPING_TYPE_ENUM.PROXY,
      },
      orderBy: {
        created_on: 'desc',
      },
      select: {
        setting: true,
      },
    });

    if (!latestProxyOutboundShipment) {
      return this.SUCCESS({ hasPermission: true, shipper, supplier });
    } else {
      // check prise
      const { ...rest } = latestProxyOutboundShipment.setting;

      return this.SUCCESS({
        hasPermission: true,
        shipper,
        supplier,
        prePrice: {
          ...rest,
        },
      });
    }
  }

  async getInventoryListOptions(user, queries) {
    const connect = this.DB.READ;

    // get user apply information
    const applyUser = await connect.users.findFirst({
      where: {
        enterprise_id: user.enterprise_id,
        staff_type: STAFF_TYPE_ENUM.ENTERPRISE,
        delete_flag: false,
      },
      select: {
        id: true,
        setting: {
          select: {
            inventory_control_type: true,
          },
        },
      },
    });
    if (!applyUser) {
      throw new BadRequestException();
    }

    // get queries
    const { type } = queries;

    const searchCondition = {
      inventory_type: type ?? undefined,
      delete_flag: false,
      user_id:
        applyUser?.setting.inventory_control_type === INVENTORY_CONTROL_TYPE_ENUM.USER
          ? user.id
          : applyUser.id,
      net_weight_inventory: {
        gt: 0,
      },
    };

    // calculate total item
    const total_item = await connect.inventories.count({
      where: searchCondition,
    });


    let items = []
    // if total item is 0, return empty items}
    if (total_item > 0) {
      items = await connect.inventories.findMany({
        where: searchCondition,
        select: {
          id: true,
          group_name: true,
          net_weight_inventory: true,
          net_weight_total: true,
          latest_arrival_date: true,
        },
        orderBy: {
          latest_arrival_date: 'desc',
        },
        // only get 100 records
        take: 100,
      });
    }

    if (user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE) {
      const typeGroups = await connect.inventories.groupBy({
        by: ['inventory_type'],
        where: {
          ...searchCondition,
          inventory_type: {
            in: [INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED, INVENTORY_TYPE_ENUM.OTHER],
          },
        },
        _count: {
          _all: true,
        },
      });

      const inventory_type_options = [
        {
          label: '国内在庫',
          value: INVENTORY_TYPE_ENUM.DOMESTIC,
        },
      ];

      if (typeGroups && typeGroups.length > 0) {
        typeGroups.forEach((item) => {
          if (item.inventory_type === INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED) {
            inventory_type_options.push({
              label: '輸入在庫',
              value: INVENTORY_TYPE_ENUM.IMPORTED_EXPORTED,
            });
          } else {
            inventory_type_options.push({
              label: '養殖在庫',
              value: INVENTORY_TYPE_ENUM.OTHER,
            });
          }
        });
      }

      return this.SUCCESS({
        items,
        inventory_type_options,
      });
    }

    return this.SUCCESS({
      items,
    });
  }
}

module.exports = OutboundService;
